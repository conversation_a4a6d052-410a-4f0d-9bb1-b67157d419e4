using UnityEngine;
using UnityEngine.AI;
using System.Collections.Generic;

public class LevelManager : MonoBehaviour
{
    [Header("Level Settings")]
    [SerializeField] private Vector3 levelSize = new Vector3(100f, 10f, 100f);
    [SerializeField] private Material terrainMaterial;
    [SerializeField] private Material pathMaterial;
    
    [Header("Terrain Generation")]
    [SerializeField] private bool generateTerrain = true;
    [SerializeField] private float terrainHeight = 5f;
    [SerializeField] private float noiseScale = 0.1f;
    [SerializeField] private AnimationCurve heightCurve = AnimationCurve.Linear(0, 0, 1, 1);
    
    [Header("Path Generation")]
    [SerializeField] private float pathWidth = 4f;
    [SerializeField] private int pathResolution = 50;
    [SerializeField] private bool smoothPaths = true;
    
    [Header("Level Objects")]
    [SerializeField] private GameObject groundPrefab;
    [SerializeField] private GameObject wallPrefab;
    [SerializeField] private GameObject decorationPrefabs;
    
    // Level components
    private GameObject terrainObject;
    private GameObject pathsObject;
    private GameObject wallsObject;
    private List<Vector3> mainPath = new List<Vector3>();
    
    // Spawn and base positions
    private Vector3 enemySpawnPosition;
    private Vector3 playerBasePosition;
    
    // Properties
    public Vector3 LevelSize => levelSize;
    public Vector3 EnemySpawnPosition => enemySpawnPosition;
    public Vector3 PlayerBasePosition => playerBasePosition;
    public List<Vector3> MainPath => mainPath;
    
    private void Awake()
    {
        GenerateLevel();
    }
    
    public void GenerateLevel()
    {
        // Clear existing level
        ClearLevel();
        
        // Create parent objects
        CreateParentObjects();
        
        // Generate terrain
        if (generateTerrain)
        {
            GenerateTerrain();
        }
        else
        {
            CreateFlatGround();
        }
        
        // Generate main path
        GenerateMainPath();
        
        // Create walls around level
        CreateBoundaryWalls();
        
        // Set spawn and base positions
        SetupSpawnAndBase();
        
        // Bake NavMesh after level generation
        Invoke(nameof(BakeNavMesh), 0.1f);
    }
    
    private void ClearLevel()
    {
        // Remove existing level objects
        if (terrainObject != null)
            DestroyImmediate(terrainObject);
        if (pathsObject != null)
            DestroyImmediate(pathsObject);
        if (wallsObject != null)
            DestroyImmediate(wallsObject);
    }
    
    private void CreateParentObjects()
    {
        terrainObject = new GameObject("Terrain");
        terrainObject.transform.SetParent(transform);
        
        pathsObject = new GameObject("Paths");
        pathsObject.transform.SetParent(transform);
        
        wallsObject = new GameObject("Walls");
        wallsObject.transform.SetParent(transform);
    }
    
    private void GenerateTerrain()
    {
        // Create terrain mesh
        GameObject terrain = GameObject.CreatePrimitive(PrimitiveType.Plane);
        terrain.name = "TerrainMesh";
        terrain.transform.SetParent(terrainObject.transform);
        terrain.transform.localScale = new Vector3(levelSize.x / 10f, 1f, levelSize.z / 10f);
        terrain.transform.position = Vector3.zero;
        
        // Apply material
        if (terrainMaterial != null)
        {
            terrain.GetComponent<Renderer>().material = terrainMaterial;
        }
        
        // Add height variation using noise
        Mesh mesh = terrain.GetComponent<MeshFilter>().mesh;
        Vector3[] vertices = mesh.vertices;
        
        for (int i = 0; i < vertices.Length; i++)
        {
            Vector3 worldPos = terrain.transform.TransformPoint(vertices[i]);
            float noiseValue = Mathf.PerlinNoise(worldPos.x * noiseScale, worldPos.z * noiseScale);
            float height = heightCurve.Evaluate(noiseValue) * terrainHeight;
            vertices[i].y = height;
        }
        
        mesh.vertices = vertices;
        mesh.RecalculateNormals();
        mesh.RecalculateBounds();
        
        // Add mesh collider
        MeshCollider collider = terrain.GetComponent<MeshCollider>();
        if (collider != null)
        {
            collider.sharedMesh = mesh;
        }
    }
    
    private void CreateFlatGround()
    {
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.SetParent(terrainObject.transform);
        ground.transform.localScale = new Vector3(levelSize.x / 10f, 1f, levelSize.z / 10f);
        ground.transform.position = Vector3.zero;
        
        if (terrainMaterial != null)
        {
            ground.GetComponent<Renderer>().material = terrainMaterial;
        }
    }
    
    private void GenerateMainPath()
    {
        mainPath.Clear();
        
        // Define start and end points
        Vector3 startPoint = new Vector3(-levelSize.x * 0.4f, 0.1f, -levelSize.z * 0.4f);
        Vector3 endPoint = new Vector3(levelSize.x * 0.4f, 0.1f, levelSize.z * 0.4f);
        
        // Create waypoints for the path
        List<Vector3> waypoints = new List<Vector3>();
        waypoints.Add(startPoint);
        
        // Add some intermediate waypoints for a more interesting path
        waypoints.Add(new Vector3(-levelSize.x * 0.2f, 0.1f, 0f));
        waypoints.Add(new Vector3(0f, 0.1f, levelSize.z * 0.2f));
        waypoints.Add(new Vector3(levelSize.x * 0.2f, 0.1f, 0f));
        waypoints.Add(endPoint);
        
        // Generate smooth path between waypoints
        for (int i = 0; i < waypoints.Count - 1; i++)
        {
            Vector3 start = waypoints[i];
            Vector3 end = waypoints[i + 1];
            
            int segments = pathResolution / (waypoints.Count - 1);
            for (int j = 0; j <= segments; j++)
            {
                float t = (float)j / segments;
                Vector3 point = Vector3.Lerp(start, end, t);
                
                // Add some curve to the path
                if (smoothPaths && i < waypoints.Count - 2)
                {
                    Vector3 nextEnd = waypoints[i + 2];
                    Vector3 curvePoint = Vector3.Lerp(end, nextEnd, t * 0.3f);
                    point = Vector3.Lerp(point, curvePoint, t * 0.5f);
                }
                
                mainPath.Add(point);
            }
        }
        
        // Create visual path
        CreatePathVisual();
    }
    
    private void CreatePathVisual()
    {
        if (mainPath.Count < 2) return;
        
        for (int i = 0; i < mainPath.Count - 1; i++)
        {
            Vector3 start = mainPath[i];
            Vector3 end = mainPath[i + 1];
            Vector3 direction = (end - start).normalized;
            Vector3 center = (start + end) * 0.5f;
            float distance = Vector3.Distance(start, end);
            
            // Create path segment
            GameObject pathSegment = GameObject.CreatePrimitive(PrimitiveType.Cube);
            pathSegment.name = $"PathSegment_{i}";
            pathSegment.transform.SetParent(pathsObject.transform);
            pathSegment.transform.position = center;
            pathSegment.transform.LookAt(center + direction);
            pathSegment.transform.localScale = new Vector3(pathWidth, 0.1f, distance);
            
            if (pathMaterial != null)
            {
                pathSegment.GetComponent<Renderer>().material = pathMaterial;
            }
            
            // Remove collider to avoid interference
            DestroyImmediate(pathSegment.GetComponent<Collider>());
        }
    }
    
    private void CreateBoundaryWalls()
    {
        if (wallPrefab == null) return;
        
        float wallHeight = 5f;
        float wallThickness = 1f;
        
        // Create walls around the level perimeter
        Vector3[] wallPositions = {
            new Vector3(0, wallHeight * 0.5f, levelSize.z * 0.5f),  // North
            new Vector3(0, wallHeight * 0.5f, -levelSize.z * 0.5f), // South
            new Vector3(levelSize.x * 0.5f, wallHeight * 0.5f, 0),  // East
            new Vector3(-levelSize.x * 0.5f, wallHeight * 0.5f, 0)  // West
        };
        
        Vector3[] wallScales = {
            new Vector3(levelSize.x, wallHeight, wallThickness),     // North
            new Vector3(levelSize.x, wallHeight, wallThickness),     // South
            new Vector3(wallThickness, wallHeight, levelSize.z),     // East
            new Vector3(wallThickness, wallHeight, levelSize.z)      // West
        };
        
        for (int i = 0; i < wallPositions.Length; i++)
        {
            GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
            wall.name = $"BoundaryWall_{i}";
            wall.transform.SetParent(wallsObject.transform);
            wall.transform.position = wallPositions[i];
            wall.transform.localScale = wallScales[i];
            
            // Add NavMesh obstacle
            NavMeshObstacle obstacle = wall.AddComponent<NavMeshObstacle>();
            obstacle.carving = true;
        }
    }
    
    private void SetupSpawnAndBase()
    {
        if (mainPath.Count > 0)
        {
            enemySpawnPosition = mainPath[0];
            playerBasePosition = mainPath[mainPath.Count - 1];
        }
        else
        {
            enemySpawnPosition = new Vector3(-levelSize.x * 0.4f, 1f, -levelSize.z * 0.4f);
            playerBasePosition = new Vector3(levelSize.x * 0.4f, 1f, levelSize.z * 0.4f);
        }
    }
    
    private void BakeNavMesh()
    {
        // This would typically be done through the NavMesh baking system
        // For runtime, we'll use NavMeshSurface if available
        NavMeshSurface surface = GetComponent<NavMeshSurface>();
        if (surface != null)
        {
            surface.BuildNavMesh();
        }
    }
    
    public Vector3 GetRandomPositionOnPath()
    {
        if (mainPath.Count == 0) return Vector3.zero;
        
        int randomIndex = Random.Range(0, mainPath.Count);
        return mainPath[randomIndex];
    }
    
    public Vector3 GetPathDirection(Vector3 position)
    {
        if (mainPath.Count < 2) return Vector3.forward;
        
        // Find closest point on path
        int closestIndex = 0;
        float closestDistance = float.MaxValue;
        
        for (int i = 0; i < mainPath.Count; i++)
        {
            float distance = Vector3.Distance(position, mainPath[i]);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestIndex = i;
            }
        }
        
        // Get direction to next point
        if (closestIndex < mainPath.Count - 1)
        {
            return (mainPath[closestIndex + 1] - mainPath[closestIndex]).normalized;
        }
        
        return Vector3.forward;
    }
}
