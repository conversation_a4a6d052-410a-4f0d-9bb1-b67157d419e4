using UnityEngine;
using UnityEngine.InputSystem;
using System.Collections;

public class PlayerShooting : MonoBehaviour
{
    [Header("Shooting Settings")]
    [SerializeField] private float damage = 25f;
    [SerializeField] private float range = 100f;
    [SerializeField] private float fireRate = 10f;
    [SerializeField] private float impactForce = 30f;
    
    [Header("Weapon Settings")]
    [SerializeField] private int maxAmmo = 30;
    [SerializeField] private int currentAmmo;
    [SerializeField] private float reloadTime = 2f;
    [SerializeField] private bool infiniteAmmo = false;
    
    [Header("Effects")]
    [SerializeField] private GameObject muzzleFlashPrefab;
    [SerializeField] private GameObject bulletImpactPrefab;
    [SerializeField] private Transform firePoint;
    [SerializeField] private LineRenderer bulletTrail;
    [SerializeField] private float trailDuration = 0.1f;
    
    [Header("Audio")]
    [SerializeField] private AudioSource audioSource;
    [SerializeField] private AudioClip shootSound;
    [SerializeField] private AudioClip reloadSound;
    [SerializeField] private AudioClip emptySound;
    
    [Header("Crosshair")]
    [SerializeField] private LayerMask shootableLayers = -1;
    [SerializeField] private bool showCrosshair = true;
    
    // Input system
    private PlayerInputActions inputActions;
    
    // Shooting state
    private float nextTimeToFire = 0f;
    private bool isReloading = false;
    private bool isShooting = false;
    private Camera playerCamera;
    
    // Properties
    public float Damage => damage;
    public float Range => range;
    public int CurrentAmmo => currentAmmo;
    public int MaxAmmo => maxAmmo;
    public bool IsReloading => isReloading;
    public bool CanShoot => !isReloading && (infiniteAmmo || currentAmmo > 0) && Time.time >= nextTimeToFire;
    
    // Events
    public System.Action<int, int> OnAmmoChanged; // current, max
    public System.Action OnReloadStarted;
    public System.Action OnReloadCompleted;
    public System.Action OnShoot;
    
    private void Awake()
    {
        inputActions = new PlayerInputActions();
        playerCamera = Camera.main;
        
        if (audioSource == null)
            audioSource = GetComponent<AudioSource>();
        
        // Initialize ammo
        currentAmmo = maxAmmo;
        
        // Setup fire point if not assigned
        if (firePoint == null)
        {
            GameObject firePointObj = new GameObject("FirePoint");
            firePointObj.transform.SetParent(playerCamera.transform);
            firePointObj.transform.localPosition = Vector3.forward;
            firePoint = firePointObj.transform;
        }
        
        // Setup bullet trail
        if (bulletTrail == null)
        {
            GameObject trailObj = new GameObject("BulletTrail");
            bulletTrail = trailObj.AddComponent<LineRenderer>();
            bulletTrail.material = new Material(Shader.Find("Sprites/Default"));
            bulletTrail.color = Color.yellow;
            bulletTrail.startWidth = 0.02f;
            bulletTrail.endWidth = 0.02f;
            bulletTrail.positionCount = 2;
            bulletTrail.enabled = false;
        }
    }
    
    private void OnEnable()
    {
        inputActions.Player.Enable();
        inputActions.Player.Shoot.performed += OnShootPerformed;
        inputActions.Player.Shoot.canceled += OnShootCanceled;
    }
    
    private void OnDisable()
    {
        inputActions.Player.Shoot.performed -= OnShootPerformed;
        inputActions.Player.Shoot.canceled -= OnShootCanceled;
        inputActions.Player.Disable();
    }
    
    private void Start()
    {
        OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);
    }
    
    private void Update()
    {
        // Handle automatic reload when ammo is empty
        if (!infiniteAmmo && currentAmmo <= 0 && !isReloading)
        {
            StartReload();
        }
        
        // Handle manual reload input
        if (Keyboard.current.rKey.wasPressedThisFrame && !isReloading && currentAmmo < maxAmmo)
        {
            StartReload();
        }
    }
    
    private void OnShootPerformed(InputAction.CallbackContext context)
    {
        isShooting = true;
        TryShoot();
    }
    
    private void OnShootCanceled(InputAction.CallbackContext context)
    {
        isShooting = false;
    }
    
    private void TryShoot()
    {
        if (!CanShoot) 
        {
            // Play empty sound if out of ammo
            if (!infiniteAmmo && currentAmmo <= 0 && audioSource && emptySound)
            {
                audioSource.PlayOneShot(emptySound);
            }
            return;
        }
        
        Shoot();
        
        // Set next fire time
        nextTimeToFire = Time.time + 1f / fireRate;
        
        // Continue shooting if holding down fire button (for automatic weapons)
        if (isShooting && fireRate > 1f)
        {
            Invoke(nameof(TryShoot), 1f / fireRate);
        }
    }
    
    private void Shoot()
    {
        // Consume ammo
        if (!infiniteAmmo)
        {
            currentAmmo--;
            OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);
        }
        
        // Play shoot sound
        if (audioSource && shootSound)
        {
            audioSource.PlayOneShot(shootSound);
        }
        
        // Show muzzle flash
        if (muzzleFlashPrefab && firePoint)
        {
            GameObject flash = Instantiate(muzzleFlashPrefab, firePoint.position, firePoint.rotation);
            Destroy(flash, 0.1f);
        }
        
        // Perform raycast
        Vector3 rayOrigin = playerCamera.transform.position;
        Vector3 rayDirection = playerCamera.transform.forward;
        
        RaycastHit hit;
        Vector3 targetPoint;
        
        if (Physics.Raycast(rayOrigin, rayDirection, out hit, range, shootableLayers))
        {
            targetPoint = hit.point;
            
            // Deal damage to enemy
            IDamageable damageable = hit.collider.GetComponent<IDamageable>();
            if (damageable != null)
            {
                damageable.TakeDamage(damage);
            }
            
            // Apply impact force to rigidbody
            Rigidbody hitRigidbody = hit.collider.GetComponent<Rigidbody>();
            if (hitRigidbody != null)
            {
                hitRigidbody.AddForce(-hit.normal * impactForce);
            }
            
            // Create impact effect
            if (bulletImpactPrefab)
            {
                GameObject impact = Instantiate(bulletImpactPrefab, hit.point, Quaternion.LookRotation(hit.normal));
                Destroy(impact, 2f);
            }
        }
        else
        {
            targetPoint = rayOrigin + rayDirection * range;
        }
        
        // Show bullet trail
        StartCoroutine(ShowBulletTrail(firePoint.position, targetPoint));
        
        OnShoot?.Invoke();
    }
    
    private IEnumerator ShowBulletTrail(Vector3 startPoint, Vector3 endPoint)
    {
        bulletTrail.enabled = true;
        bulletTrail.SetPosition(0, startPoint);
        bulletTrail.SetPosition(1, endPoint);
        
        yield return new WaitForSeconds(trailDuration);
        
        bulletTrail.enabled = false;
    }
    
    public void StartReload()
    {
        if (isReloading || currentAmmo >= maxAmmo) return;
        
        StartCoroutine(ReloadCoroutine());
    }
    
    private IEnumerator ReloadCoroutine()
    {
        isReloading = true;
        OnReloadStarted?.Invoke();
        
        // Play reload sound
        if (audioSource && reloadSound)
        {
            audioSource.PlayOneShot(reloadSound);
        }
        
        yield return new WaitForSeconds(reloadTime);
        
        currentAmmo = maxAmmo;
        isReloading = false;
        
        OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);
        OnReloadCompleted?.Invoke();
    }
    
    // Public methods for other systems
    public void SetDamage(float newDamage)
    {
        damage = Mathf.Max(0f, newDamage);
    }
    
    public void SetFireRate(float newFireRate)
    {
        fireRate = Mathf.Max(0.1f, newFireRate);
    }
    
    public void SetRange(float newRange)
    {
        range = Mathf.Max(1f, newRange);
    }
    
    public void AddAmmo(int amount)
    {
        if (!infiniteAmmo)
        {
            currentAmmo = Mathf.Min(currentAmmo + amount, maxAmmo);
            OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);
        }
    }
    
    public void SetInfiniteAmmo(bool infinite)
    {
        infiniteAmmo = infinite;
        if (infinite)
        {
            currentAmmo = maxAmmo;
            OnAmmoChanged?.Invoke(currentAmmo, maxAmmo);
        }
    }
}
