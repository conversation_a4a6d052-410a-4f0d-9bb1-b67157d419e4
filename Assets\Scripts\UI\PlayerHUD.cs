using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class PlayerHUD : MonoBehaviour
{
    [Header("Crosshair")]
    [SerializeField] private GameObject crosshair;
    [SerializeField] private Image crosshairImage;
    [SerializeField] private Color defaultCrosshairColor = Color.white;
    [SerializeField] private Color enemyCrosshairColor = Color.red;
    [SerializeField] private float crosshairSize = 20f;
    
    [Header("Health Display")]
    [SerializeField] private Slider healthBar;
    [SerializeField] private TextMeshProUGUI healthText;
    [SerializeField] private Image healthBarFill;
    [SerializeField] private Color healthColorHigh = Color.green;
    [SerializeField] private Color healthColorMid = Color.yellow;
    [SerializeField] private Color healthColorLow = Color.red;
    
    [Header("Ammo Display")]
    [SerializeField] private TextMeshProUGUI ammoText;
    [SerializeField] private TextMeshProUGUI reloadText;
    [SerializeField] private Image ammoIcon;
    
    [Header("Resource Display")]
    [SerializeField] private TextMeshProUGUI currencyText;
    [SerializeField] private Image currencyIcon;
    
    [Header("Wave Information")]
    [SerializeField] private TextMeshProUGUI waveText;
    [SerializeField] private TextMeshProUGUI enemiesRemainingText;
    [SerializeField] private Slider waveProgressBar;
    
    [Header("Interaction Prompts")]
    [SerializeField] private GameObject interactionPrompt;
    [SerializeField] private TextMeshProUGUI interactionText;
    
    [Header("Game State")]
    [SerializeField] private GameObject pauseMenu;
    [SerializeField] private GameObject gameOverScreen;
    [SerializeField] private GameObject victoryScreen;
    
    // References
    private PlayerShooting playerShooting;
    private Camera playerCamera;
    
    // State
    private bool isInitialized = false;
    
    private void Awake()
    {
        // Find player components
        playerShooting = FindObjectOfType<PlayerShooting>();
        playerCamera = Camera.main;
        
        // Initialize UI elements
        InitializeUI();
    }
    
    private void Start()
    {
        // Subscribe to events
        if (playerShooting != null)
        {
            playerShooting.OnAmmoChanged += UpdateAmmoDisplay;
            playerShooting.OnReloadStarted += ShowReloadText;
            playerShooting.OnReloadCompleted += HideReloadText;
        }
        
        isInitialized = true;
        
        // Initial UI update
        UpdateCrosshair();
        HideReloadText();
        HideInteractionPrompt();
    }
    
    private void Update()
    {
        if (!isInitialized) return;
        
        UpdateCrosshair();
        CheckForInteractables();
    }
    
    private void InitializeUI()
    {
        // Setup crosshair
        if (crosshair != null && crosshairImage != null)
        {
            crosshairImage.color = defaultCrosshairColor;
            RectTransform crosshairRect = crosshair.GetComponent<RectTransform>();
            if (crosshairRect != null)
            {
                crosshairRect.sizeDelta = Vector2.one * crosshairSize;
            }
        }
        
        // Setup health bar
        if (healthBar != null)
        {
            healthBar.minValue = 0f;
            healthBar.maxValue = 100f;
            healthBar.value = 100f;
        }
        
        // Initialize text elements
        if (healthText != null)
            healthText.text = "100 / 100";
        
        if (ammoText != null)
            ammoText.text = "30 / 30";
        
        if (currencyText != null)
            currencyText.text = "0";
        
        if (waveText != null)
            waveText.text = "Wave 1";
        
        if (enemiesRemainingText != null)
            enemiesRemainingText.text = "Enemies: 0";
    }
    
    private void UpdateCrosshair()
    {
        if (crosshairImage == null || playerCamera == null) return;
        
        // Raycast to check if aiming at enemy
        Ray ray = playerCamera.ScreenPointToRay(new Vector3(Screen.width / 2, Screen.height / 2, 0));
        RaycastHit hit;
        
        bool aimingAtEnemy = false;
        if (Physics.Raycast(ray, out hit, 100f))
        {
            // Check if hit object has enemy tag or IDamageable component
            if (hit.collider.CompareTag("Enemy") || hit.collider.GetComponent<IDamageable>() != null)
            {
                aimingAtEnemy = true;
            }
        }
        
        // Update crosshair color
        crosshairImage.color = aimingAtEnemy ? enemyCrosshairColor : defaultCrosshairColor;
    }
    
    private void CheckForInteractables()
    {
        if (playerCamera == null) return;
        
        Ray ray = playerCamera.ScreenPointToRay(new Vector3(Screen.width / 2, Screen.height / 2, 0));
        RaycastHit hit;
        
        if (Physics.Raycast(ray, out hit, 5f))
        {
            // Check for interactable objects
            IInteractable interactable = hit.collider.GetComponent<IInteractable>();
            if (interactable != null)
            {
                ShowInteractionPrompt(interactable.GetInteractionText());
                return;
            }
        }
        
        HideInteractionPrompt();
    }
    
    public void UpdateHealthDisplay(float currentHealth, float maxHealth)
    {
        if (healthBar != null)
        {
            healthBar.maxValue = maxHealth;
            healthBar.value = currentHealth;
        }
        
        if (healthText != null)
        {
            healthText.text = $"{currentHealth:F0} / {maxHealth:F0}";
        }
        
        // Update health bar color based on health percentage
        if (healthBarFill != null)
        {
            float healthPercentage = currentHealth / maxHealth;
            
            if (healthPercentage > 0.6f)
                healthBarFill.color = healthColorHigh;
            else if (healthPercentage > 0.3f)
                healthBarFill.color = healthColorMid;
            else
                healthBarFill.color = healthColorLow;
        }
    }
    
    private void UpdateAmmoDisplay(int currentAmmo, int maxAmmo)
    {
        if (ammoText != null)
        {
            ammoText.text = $"{currentAmmo} / {maxAmmo}";
            
            // Change color based on ammo level
            if (currentAmmo <= 0)
                ammoText.color = Color.red;
            else if (currentAmmo <= maxAmmo * 0.3f)
                ammoText.color = Color.yellow;
            else
                ammoText.color = Color.white;
        }
    }
    
    public void UpdateCurrencyDisplay(int currency)
    {
        if (currencyText != null)
        {
            currencyText.text = currency.ToString();
        }
    }
    
    public void UpdateWaveDisplay(int currentWave, int enemiesRemaining)
    {
        if (waveText != null)
        {
            waveText.text = $"Wave {currentWave}";
        }
        
        if (enemiesRemainingText != null)
        {
            enemiesRemainingText.text = $"Enemies: {enemiesRemaining}";
        }
    }
    
    public void UpdateWaveProgress(float progress)
    {
        if (waveProgressBar != null)
        {
            waveProgressBar.value = progress;
        }
    }
    
    private void ShowReloadText()
    {
        if (reloadText != null)
        {
            reloadText.gameObject.SetActive(true);
            reloadText.text = "RELOADING...";
        }
    }
    
    private void HideReloadText()
    {
        if (reloadText != null)
        {
            reloadText.gameObject.SetActive(false);
        }
    }
    
    public void ShowInteractionPrompt(string text)
    {
        if (interactionPrompt != null && interactionText != null)
        {
            interactionPrompt.SetActive(true);
            interactionText.text = text;
        }
    }
    
    public void HideInteractionPrompt()
    {
        if (interactionPrompt != null)
        {
            interactionPrompt.SetActive(false);
        }
    }
    
    public void ShowPauseMenu()
    {
        if (pauseMenu != null)
        {
            pauseMenu.SetActive(true);
            Time.timeScale = 0f;
        }
    }
    
    public void HidePauseMenu()
    {
        if (pauseMenu != null)
        {
            pauseMenu.SetActive(false);
            Time.timeScale = 1f;
        }
    }
    
    public void ShowGameOverScreen()
    {
        if (gameOverScreen != null)
        {
            gameOverScreen.SetActive(true);
        }
    }
    
    public void ShowVictoryScreen()
    {
        if (victoryScreen != null)
        {
            victoryScreen.SetActive(true);
        }
    }
    
    public void SetCrosshairVisible(bool visible)
    {
        if (crosshair != null)
        {
            crosshair.SetActive(visible);
        }
    }
    
    public void SetCrosshairSize(float size)
    {
        crosshairSize = size;
        if (crosshair != null)
        {
            RectTransform crosshairRect = crosshair.GetComponent<RectTransform>();
            if (crosshairRect != null)
            {
                crosshairRect.sizeDelta = Vector2.one * crosshairSize;
            }
        }
    }
    
    private void OnDestroy()
    {
        // Unsubscribe from events
        if (playerShooting != null)
        {
            playerShooting.OnAmmoChanged -= UpdateAmmoDisplay;
            playerShooting.OnReloadStarted -= ShowReloadText;
            playerShooting.OnReloadCompleted -= HideReloadText;
        }
    }
}
