using UnityEngine;

/// <summary>
/// Interface for objects that can be interacted with by the player
/// </summary>
public interface IInteractable
{
    /// <summary>
    /// Whether this object can currently be interacted with
    /// </summary>
    bool CanInteract { get; }
    
    /// <summary>
    /// The text to display when the player is looking at this object
    /// </summary>
    /// <returns>Interaction prompt text</returns>
    string GetInteractionText();
    
    /// <summary>
    /// Called when the player interacts with this object
    /// </summary>
    /// <param name="player">The player GameObject that is interacting</param>
    void Interact(GameObject player);
    
    /// <summary>
    /// Called when the player starts looking at this object
    /// </summary>
    /// <param name="player">The player GameObject that is looking</param>
    void OnLookStart(GameObject player);
    
    /// <summary>
    /// Called when the player stops looking at this object
    /// </summary>
    /// <param name="player">The player GameObject that was looking</param>
    void OnLookEnd(GameObject player);
}
