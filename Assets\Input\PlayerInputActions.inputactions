{"name": "PlayerInputActions", "maps": [{"name": "Player", "id": "f62a4b92-ef5e-4175-8f4c-c9b2c0e7f8a1", "actions": [{"name": "Move", "type": "Value", "id": "6bc1aaf4-b110-4ff7-891e-5b9fe6f32c4d", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Look", "type": "Value", "id": "2690c379-f54d-45be-a724-414123833eb4", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "8c4abdf8-4099-493a-aa1a-129cc1e07d12", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Shoot", "type": "<PERSON><PERSON>", "id": "92c84444-24e6-4f32-b0a9-1ac7e6d6b5a2", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "PlaceTower", "type": "<PERSON><PERSON>", "id": "a1f4e6d8-7b2c-4c9d-8e5f-3a6b9c0d1e2f", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "OpenShop", "type": "<PERSON><PERSON>", "id": "b2e5f7a9-8c3d-4d0e-9f6a-4b7c0d2e3f4a", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Pause", "type": "<PERSON><PERSON>", "id": "c3f6a8ba-9d4e-4e1f-0a7b-5c8d1e3f4a5b", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "WASD", "id": "b7594ddb-26c9-4f2b-b3f4-1d0c6c5e8f9a", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "d8e5f6a7-b9c0-4d1e-8f2a-6b7c8d9e0f1a", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "e9f6a7b8-c0d1-4e2f-9a3b-7c8d9e0f1a2b", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "f0a7b8c9-d1e2-4f3a-0b4c-8d9e0f1a2b3c", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "a1b8c9d0-e2f3-4a5b-1c6d-9e0f1a2b3c4d", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "b2c9d0e1-f3a4-4b6c-2d7e-0f1a2b3c4d5e", "path": "<Mouse>/delta", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "c3d0e1f2-a4b5-4c7d-3e8f-1a2b3c4d5e6f", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d4e1f2a3-b5c6-4d8e-4f9a-2b3c4d5e6f7a", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Shoot", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e5f2a3b4-c6d7-4e9f-5a0b-3c4d5e6f7a8b", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "PlaceTower", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f6a3b4c5-d7e8-4f0a-6b1c-4d5e6f7a8b9c", "path": "<Keyboard>/tab", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "OpenShop", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "a7b4c5d6-e8f9-4a1b-7c2d-5e6f7a8b9c0d", "path": "<Keyboard>/escape", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Pause", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}]}