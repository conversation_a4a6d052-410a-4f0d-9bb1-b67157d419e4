using UnityEngine;
using System.Collections.Generic;
using System.Collections;

public class SpawnPointManager : MonoBehaviour
{
    [Header("Spawn Point Settings")]
    [SerializeField] private List<Transform> spawnPoints = new List<Transform>();
    [SerializeField] private float spawnRadius = 2f;
    [SerializeField] private LayerMask obstacleLayerMask = 1;
    [SerializeField] private bool autoCreateSpawnPoints = true;
    [SerializeField] private int autoSpawnPointCount = 5;
    
    [Header("Spawn Validation")]
    [SerializeField] private float minDistanceFromPlayer = 10f;
    [SerializeField] private float minDistanceBetweenSpawns = 3f;
    [SerializeField] private bool validateSpawnPositions = true;
    
    [Header("Visual Debug")]
    [SerializeField] private bool showSpawnGizmos = true;
    [SerializeField] private Color spawnGizmoColor = Color.red;
    [SerializeField] private GameObject spawnEffectPrefab;
    
    // References
    private LevelManager levelManager;
    private Transform playerTransform;
    
    // Spawn tracking
    private Dictionary<Transform, float> lastSpawnTimes = new Dictionary<Transform, float>();
    private List<Vector3> recentSpawnPositions = new List<Vector3>();
    
    // Properties
    public int SpawnPointCount => spawnPoints.Count;
    public List<Transform> SpawnPoints => spawnPoints;
    
    // Events
    public System.Action<Vector3, GameObject> OnEnemySpawned;
    
    private void Awake()
    {
        levelManager = FindObjectOfType<LevelManager>();
        
        // Find player
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            playerTransform = player.transform;
        }
        
        InitializeSpawnPoints();
    }
    
    private void Start()
    {
        if (autoCreateSpawnPoints && spawnPoints.Count == 0)
        {
            CreateAutoSpawnPoints();
        }
        
        ValidateAllSpawnPoints();
    }
    
    private void InitializeSpawnPoints()
    {
        // Initialize spawn time tracking
        foreach (Transform spawnPoint in spawnPoints)
        {
            if (spawnPoint != null)
            {
                lastSpawnTimes[spawnPoint] = 0f;
            }
        }
    }
    
    private void CreateAutoSpawnPoints()
    {
        if (levelManager == null) return;
        
        Vector3 levelSize = levelManager.LevelSize;
        Vector3 enemySpawnArea = levelManager.EnemySpawnPosition;
        
        // Create spawn points around the enemy spawn area
        for (int i = 0; i < autoSpawnPointCount; i++)
        {
            float angle = (360f / autoSpawnPointCount) * i * Mathf.Deg2Rad;
            float distance = spawnRadius * 2f;
            
            Vector3 spawnPosition = enemySpawnArea + new Vector3(
                Mathf.Cos(angle) * distance,
                0f,
                Mathf.Sin(angle) * distance
            );
            
            // Validate position
            if (IsValidSpawnPosition(spawnPosition))
            {
                CreateSpawnPoint(spawnPosition, $"AutoSpawnPoint_{i}");
            }
        }
    }
    
    private void CreateSpawnPoint(Vector3 position, string name)
    {
        GameObject spawnPointObj = new GameObject(name);
        spawnPointObj.transform.SetParent(transform);
        spawnPointObj.transform.position = position;
        
        // Add visual indicator
        GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        indicator.name = "SpawnIndicator";
        indicator.transform.SetParent(spawnPointObj.transform);
        indicator.transform.localPosition = Vector3.zero;
        indicator.transform.localScale = new Vector3(spawnRadius * 2f, 0.1f, spawnRadius * 2f);
        
        // Make it semi-transparent
        Renderer renderer = indicator.GetComponent<Renderer>();
        Material mat = new Material(Shader.Find("Standard"));
        mat.color = new Color(spawnGizmoColor.r, spawnGizmoColor.g, spawnGizmoColor.b, 0.3f);
        mat.SetFloat("_Mode", 3); // Transparent mode
        mat.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
        mat.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
        mat.SetInt("_ZWrite", 0);
        mat.DisableKeyword("_ALPHATEST_ON");
        mat.EnableKeyword("_ALPHABLEND_ON");
        mat.DisableKeyword("_ALPHAPREMULTIPLY_ON");
        mat.renderQueue = 3000;
        renderer.material = mat;
        
        // Remove collider
        DestroyImmediate(indicator.GetComponent<Collider>());
        
        spawnPoints.Add(spawnPointObj.transform);
        lastSpawnTimes[spawnPointObj.transform] = 0f;
    }
    
    public Vector3 GetRandomSpawnPosition()
    {
        if (spawnPoints.Count == 0) return Vector3.zero;
        
        List<Transform> availableSpawnPoints = GetAvailableSpawnPoints();
        
        if (availableSpawnPoints.Count == 0)
        {
            // If no spawn points are available, use any spawn point
            availableSpawnPoints = new List<Transform>(spawnPoints);
        }
        
        Transform selectedSpawnPoint = availableSpawnPoints[Random.Range(0, availableSpawnPoints.Count)];
        Vector3 basePosition = selectedSpawnPoint.position;
        
        // Add random offset within spawn radius
        Vector2 randomCircle = Random.insideUnitCircle * spawnRadius;
        Vector3 spawnPosition = basePosition + new Vector3(randomCircle.x, 0f, randomCircle.y);
        
        // Validate and adjust position if needed
        spawnPosition = ValidateAndAdjustPosition(spawnPosition);
        
        // Update last spawn time
        lastSpawnTimes[selectedSpawnPoint] = Time.time;
        
        // Track recent spawn position
        recentSpawnPositions.Add(spawnPosition);
        if (recentSpawnPositions.Count > 10)
        {
            recentSpawnPositions.RemoveAt(0);
        }
        
        return spawnPosition;
    }
    
    private List<Transform> GetAvailableSpawnPoints()
    {
        List<Transform> available = new List<Transform>();
        float currentTime = Time.time;
        
        foreach (Transform spawnPoint in spawnPoints)
        {
            if (spawnPoint == null) continue;
            
            // Check if enough time has passed since last spawn
            if (currentTime - lastSpawnTimes[spawnPoint] >= minDistanceBetweenSpawns)
            {
                // Check distance from player
                if (playerTransform == null || 
                    Vector3.Distance(spawnPoint.position, playerTransform.position) >= minDistanceFromPlayer)
                {
                    available.Add(spawnPoint);
                }
            }
        }
        
        return available;
    }
    
    private bool IsValidSpawnPosition(Vector3 position)
    {
        if (!validateSpawnPositions) return true;
        
        // Check for obstacles
        if (Physics.CheckSphere(position, spawnRadius * 0.5f, obstacleLayerMask))
        {
            return false;
        }
        
        // Check distance from player
        if (playerTransform != null && 
            Vector3.Distance(position, playerTransform.position) < minDistanceFromPlayer)
        {
            return false;
        }
        
        // Check distance from recent spawns
        foreach (Vector3 recentSpawn in recentSpawnPositions)
        {
            if (Vector3.Distance(position, recentSpawn) < minDistanceBetweenSpawns)
            {
                return false;
            }
        }
        
        return true;
    }
    
    private Vector3 ValidateAndAdjustPosition(Vector3 position)
    {
        if (IsValidSpawnPosition(position))
        {
            return position;
        }
        
        // Try to find a valid position nearby
        for (int attempts = 0; attempts < 10; attempts++)
        {
            Vector2 randomOffset = Random.insideUnitCircle * spawnRadius;
            Vector3 adjustedPosition = position + new Vector3(randomOffset.x, 0f, randomOffset.y);
            
            if (IsValidSpawnPosition(adjustedPosition))
            {
                return adjustedPosition;
            }
        }
        
        // If no valid position found, return original position
        return position;
    }
    
    private void ValidateAllSpawnPoints()
    {
        List<Transform> invalidSpawnPoints = new List<Transform>();
        
        foreach (Transform spawnPoint in spawnPoints)
        {
            if (spawnPoint == null || !IsValidSpawnPosition(spawnPoint.position))
            {
                invalidSpawnPoints.Add(spawnPoint);
            }
        }
        
        // Remove invalid spawn points
        foreach (Transform invalidPoint in invalidSpawnPoints)
        {
            spawnPoints.Remove(invalidPoint);
            if (lastSpawnTimes.ContainsKey(invalidPoint))
            {
                lastSpawnTimes.Remove(invalidPoint);
            }
        }
        
        Debug.Log($"SpawnPointManager: Validated {spawnPoints.Count} spawn points, removed {invalidSpawnPoints.Count} invalid points.");
    }
    
    public void SpawnEnemy(GameObject enemyPrefab)
    {
        if (enemyPrefab == null) return;
        
        Vector3 spawnPosition = GetRandomSpawnPosition();
        GameObject spawnedEnemy = Instantiate(enemyPrefab, spawnPosition, Quaternion.identity);
        
        // Play spawn effect
        if (spawnEffectPrefab != null)
        {
            GameObject effect = Instantiate(spawnEffectPrefab, spawnPosition, Quaternion.identity);
            Destroy(effect, 3f);
        }
        
        OnEnemySpawned?.Invoke(spawnPosition, spawnedEnemy);
    }
    
    public void AddSpawnPoint(Transform spawnPoint)
    {
        if (spawnPoint != null && !spawnPoints.Contains(spawnPoint))
        {
            spawnPoints.Add(spawnPoint);
            lastSpawnTimes[spawnPoint] = 0f;
        }
    }
    
    public void RemoveSpawnPoint(Transform spawnPoint)
    {
        if (spawnPoints.Contains(spawnPoint))
        {
            spawnPoints.Remove(spawnPoint);
            if (lastSpawnTimes.ContainsKey(spawnPoint))
            {
                lastSpawnTimes.Remove(spawnPoint);
            }
        }
    }
    
    public void ClearRecentSpawns()
    {
        recentSpawnPositions.Clear();
    }
    
    private void OnDrawGizmosSelected()
    {
        if (!showSpawnGizmos) return;
        
        Gizmos.color = spawnGizmoColor;
        
        foreach (Transform spawnPoint in spawnPoints)
        {
            if (spawnPoint == null) continue;
            
            // Draw spawn radius
            Gizmos.DrawWireSphere(spawnPoint.position, spawnRadius);
            
            // Draw spawn point marker
            Gizmos.DrawCube(spawnPoint.position + Vector3.up * 0.5f, Vector3.one * 0.5f);
        }
        
        // Draw recent spawn positions
        Gizmos.color = Color.yellow;
        foreach (Vector3 recentSpawn in recentSpawnPositions)
        {
            Gizmos.DrawWireSphere(recentSpawn, 0.5f);
        }
    }
}
