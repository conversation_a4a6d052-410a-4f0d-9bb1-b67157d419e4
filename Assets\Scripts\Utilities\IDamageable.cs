using UnityEngine;

/// <summary>
/// Interface for objects that can take damage
/// </summary>
public interface IDamageable
{
    /// <summary>
    /// Current health of the object
    /// </summary>
    float Health { get; }
    
    /// <summary>
    /// Maximum health of the object
    /// </summary>
    float MaxHealth { get; }
    
    /// <summary>
    /// Whether the object is currently alive/active
    /// </summary>
    bool IsAlive { get; }
    
    /// <summary>
    /// Apply damage to this object
    /// </summary>
    /// <param name="damage">Amount of damage to apply</param>
    void TakeDamage(float damage);
    
    /// <summary>
    /// Apply damage to this object with additional information
    /// </summary>
    /// <param name="damage">Amount of damage to apply</param>
    /// <param name="damageSource">Source of the damage (optional)</param>
    /// <param name="damageType">Type of damage (optional)</param>
    void TakeDamage(float damage, GameObject damageSource = null, DamageType damageType = DamageType.Normal);
    
    /// <summary>
    /// Heal this object
    /// </summary>
    /// <param name="healAmount">Amount to heal</param>
    void Heal(float healAmount);
    
    /// <summary>
    /// Kill this object instantly
    /// </summary>
    void Die();
}

/// <summary>
/// Types of damage that can be applied
/// </summary>
public enum DamageType
{
    Normal,
    Fire,
    Ice,
    Poison,
    Explosive,
    Piercing
}
