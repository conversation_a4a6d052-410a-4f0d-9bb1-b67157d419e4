using UnityEngine;
using UnityEngine.InputSystem;

public class MouseLook : Mono<PERSON>ehaviour
{
    [Header("Mouse Look Settings")]
    [SerializeField] private float mouseSensitivity = 100f;
    [SerializeField] private float verticalLookLimit = 80f;
    [SerializeField] private bool invertY = false;
    [SerializeField] private float smoothTime = 0.1f;
    
    [Header("References")]
    [SerializeField] private Transform playerBody;
    [SerializeField] private Camera playerCamera;
    
    // Input system
    private PlayerInputActions inputActions;
    
    // Mouse look variables
    private Vector2 lookInput;
    private float xRotation = 0f;
    private Vector2 currentMouseDelta;
    private Vector2 currentMouseDeltaVelocity;
    
    // State
    private bool isLookingEnabled = true;
    
    // Properties
    public float MouseSensitivity 
    { 
        get => mouseSensitivity; 
        set => mouseSensitivity = Mathf.Clamp(value, 0.1f, 500f); 
    }
    
    public bool IsLookingEnabled 
    { 
        get => isLookingEnabled; 
        set => isLookingEnabled = value; 
    }
    
    private void Awake()
    {
        inputActions = new PlayerInputActions();
        
        // Auto-assign references if not set
        if (playerCamera == null)
            playerCamera = GetComponent<Camera>();
        
        if (playerBody == null)
            playerBody = transform.parent;
        
        // Lock cursor to center of screen
        SetCursorLock(true);
    }
    
    private void OnEnable()
    {
        inputActions.Player.Enable();
        inputActions.Player.Look.performed += OnLookPerformed;
        inputActions.Player.Look.canceled += OnLookCanceled;
    }
    
    private void OnDisable()
    {
        inputActions.Player.Look.performed -= OnLookPerformed;
        inputActions.Player.Look.canceled -= OnLookCanceled;
        inputActions.Player.Disable();
    }
    
    private void Start()
    {
        // Initialize rotation based on current transform
        xRotation = transform.localEulerAngles.x;
        
        // Normalize x rotation to -180 to 180 range
        if (xRotation > 180f)
            xRotation -= 360f;
    }
    
    private void Update()
    {
        if (isLookingEnabled)
        {
            HandleMouseLook();
        }
        
        // Handle cursor lock toggle (for debugging/UI)
        if (Keyboard.current.leftAltKey.wasPressedThisFrame)
        {
            ToggleCursorLock();
        }
    }
    
    private void HandleMouseLook()
    {
        // Smooth the mouse input
        currentMouseDelta = Vector2.SmoothDamp(
            currentMouseDelta, 
            lookInput, 
            ref currentMouseDeltaVelocity, 
            smoothTime
        );
        
        // Calculate rotation amounts
        float mouseX = currentMouseDelta.x * mouseSensitivity * Time.deltaTime;
        float mouseY = currentMouseDelta.y * mouseSensitivity * Time.deltaTime;
        
        // Apply Y inversion if enabled
        if (invertY)
            mouseY = -mouseY;
        
        // Rotate the player body around Y axis (horizontal look)
        if (playerBody != null)
        {
            playerBody.Rotate(Vector3.up * mouseX);
        }
        else
        {
            transform.Rotate(Vector3.up * mouseX);
        }
        
        // Rotate the camera around X axis (vertical look)
        xRotation -= mouseY;
        xRotation = Mathf.Clamp(xRotation, -verticalLookLimit, verticalLookLimit);
        
        transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
    }
    
    private void OnLookPerformed(InputAction.CallbackContext context)
    {
        lookInput = context.ReadValue<Vector2>();
    }
    
    private void OnLookCanceled(InputAction.CallbackContext context)
    {
        lookInput = Vector2.zero;
    }
    
    public void SetCursorLock(bool locked)
    {
        if (locked)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
        else
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }
    }
    
    public void ToggleCursorLock()
    {
        bool isLocked = Cursor.lockState == CursorLockMode.Locked;
        SetCursorLock(!isLocked);
    }
    
    public void SetSensitivity(float sensitivity)
    {
        MouseSensitivity = sensitivity;
    }
    
    public void SetInvertY(bool invert)
    {
        invertY = invert;
    }
    
    public void ResetRotation()
    {
        xRotation = 0f;
        transform.localRotation = Quaternion.identity;
        
        if (playerBody != null)
        {
            playerBody.rotation = Quaternion.identity;
        }
    }
    
    public void SetLookDirection(Vector3 direction)
    {
        if (playerBody != null)
        {
            // Calculate horizontal rotation
            Vector3 horizontalDirection = new Vector3(direction.x, 0, direction.z).normalized;
            playerBody.rotation = Quaternion.LookRotation(horizontalDirection);
            
            // Calculate vertical rotation
            float angle = Mathf.Asin(direction.y) * Mathf.Rad2Deg;
            xRotation = -angle;
            xRotation = Mathf.Clamp(xRotation, -verticalLookLimit, verticalLookLimit);
            transform.localRotation = Quaternion.Euler(xRotation, 0f, 0f);
        }
    }
    
    // Public methods for other systems
    public void EnableLook()
    {
        isLookingEnabled = true;
        SetCursorLock(true);
    }
    
    public void DisableLook()
    {
        isLookingEnabled = false;
        lookInput = Vector2.zero;
        currentMouseDelta = Vector2.zero;
    }
    
    private void OnApplicationFocus(bool hasFocus)
    {
        if (hasFocus && isLookingEnabled)
        {
            SetCursorLock(true);
        }
    }
}
