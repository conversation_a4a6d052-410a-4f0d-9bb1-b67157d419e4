using UnityEngine;

public class GroundChecker : MonoBehaviour
{
    [Header("Ground Check Settings")]
    [SerializeField] private float checkRadius = 0.3f;
    [SerializeField] private float checkDistance = 0.1f;
    [SerializeField] private LayerMask groundMask = 1;
    [SerializeField] private int raycastCount = 5;
    
    [Header("Slope Detection")]
    [SerializeField] private float maxSlopeAngle = 45f;
    [SerializeField] private bool checkSlope = true;
    
    [Header("Debug")]
    [SerializeField] private bool showDebugGizmos = true;
    
    // Ground state
    private bool isGrounded;
    private bool isOnSlope;
    private Vector3 groundNormal;
    private float groundAngle;
    private RaycastHit groundHit;
    
    // Properties
    public bool IsGrounded => isGrounded;
    public bool IsOnSlope => isOnSlope;
    public Vector3 GroundNormal => groundNormal;
    public float GroundAngle => groundAngle;
    public RaycastHit GroundHit => groundHit;
    public bool IsValidSlope => groundAngle <= maxSlopeAngle;
    
    private void Update()
    {
        CheckGround();
    }
    
    private void CheckGround()
    {
        isGrounded = false;
        isOnSlope = false;
        groundNormal = Vector3.up;
        groundAngle = 0f;
        
        Vector3 checkPosition = transform.position;
        
        // Primary ground check using sphere cast
        if (Physics.SphereCast(checkPosition, checkRadius, Vector3.down, out groundHit, checkDistance, groundMask))
        {
            isGrounded = true;
            groundNormal = groundHit.normal;
            groundAngle = Vector3.Angle(Vector3.up, groundNormal);
            
            if (checkSlope && groundAngle > 0.1f)
            {
                isOnSlope = true;
            }
        }
        else
        {
            // Secondary check using multiple raycasts for more precise detection
            CheckWithRaycasts(checkPosition);
        }
    }
    
    private void CheckWithRaycasts(Vector3 center)
    {
        float angleStep = 360f / raycastCount;
        
        for (int i = 0; i < raycastCount; i++)
        {
            float angle = i * angleStep * Mathf.Deg2Rad;
            Vector3 rayOrigin = center + new Vector3(
                Mathf.Cos(angle) * checkRadius,
                0,
                Mathf.Sin(angle) * checkRadius
            );
            
            if (Physics.Raycast(rayOrigin, Vector3.down, out RaycastHit hit, checkDistance, groundMask))
            {
                isGrounded = true;
                groundHit = hit;
                groundNormal = hit.normal;
                groundAngle = Vector3.Angle(Vector3.up, groundNormal);
                
                if (checkSlope && groundAngle > 0.1f)
                {
                    isOnSlope = true;
                }
                break;
            }
        }
    }
    
    public Vector3 GetSlopeMovementDirection(Vector3 inputDirection)
    {
        if (!isOnSlope || !IsValidSlope)
            return inputDirection;
        
        // Project movement direction onto the slope
        return Vector3.ProjectOnPlane(inputDirection, groundNormal).normalized;
    }
    
    public bool CanJump()
    {
        return isGrounded && (!isOnSlope || IsValidSlope);
    }
    
    public float GetGroundDistance()
    {
        if (isGrounded)
            return groundHit.distance;
        
        // If not grounded, check how far the ground is
        if (Physics.Raycast(transform.position, Vector3.down, out RaycastHit hit, Mathf.Infinity, groundMask))
        {
            return hit.distance;
        }
        
        return Mathf.Infinity;
    }
    
    public void SetGroundMask(LayerMask mask)
    {
        groundMask = mask;
    }
    
    public void SetCheckRadius(float radius)
    {
        checkRadius = Mathf.Max(0.1f, radius);
    }
    
    public void SetCheckDistance(float distance)
    {
        checkDistance = Mathf.Max(0.01f, distance);
    }
    
    public void SetMaxSlopeAngle(float angle)
    {
        maxSlopeAngle = Mathf.Clamp(angle, 0f, 90f);
    }
    
    private void OnDrawGizmosSelected()
    {
        if (!showDebugGizmos) return;
        
        Vector3 position = transform.position;
        
        // Draw ground check sphere
        Gizmos.color = isGrounded ? Color.green : Color.red;
        Gizmos.DrawWireSphere(position, checkRadius);
        
        // Draw check distance
        Gizmos.color = Color.yellow;
        Gizmos.DrawLine(position, position + Vector3.down * checkDistance);
        
        // Draw ground normal if grounded
        if (isGrounded)
        {
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(groundHit.point, groundHit.point + groundNormal * 2f);
            
            // Draw slope indicator
            if (isOnSlope)
            {
                Gizmos.color = IsValidSlope ? Color.green : Color.red;
                Gizmos.DrawWireCube(groundHit.point, Vector3.one * 0.2f);
            }
        }
        
        // Draw raycast points
        if (raycastCount > 0)
        {
            Gizmos.color = Color.cyan;
            float angleStep = 360f / raycastCount;
            
            for (int i = 0; i < raycastCount; i++)
            {
                float angle = i * angleStep * Mathf.Deg2Rad;
                Vector3 rayOrigin = position + new Vector3(
                    Mathf.Cos(angle) * checkRadius,
                    0,
                    Mathf.Sin(angle) * checkRadius
                );
                
                Gizmos.DrawLine(rayOrigin, rayOrigin + Vector3.down * checkDistance);
            }
        }
    }
}
